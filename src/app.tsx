import Footer from '@/components/Footer';
import RightContent from '@/components/RightContent';
import { Settings as LayoutSettings } from '@ant-design/pro-components';
import { history, RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { message } from 'antd';
import defaultSettings, { isMicroservice } from 'config/defaultSettings';
import UnAccessible from '../src/pages/403';
import { errorConfig } from './requestErrorConfig';
import { currentUser } from './services/http/user';
import { zabbix } from './services/zabbix';
import { BASE_URL } from './utils/setting';

// const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: RK_API.UserAuthenticationResponse;
  loading?: boolean;
  fetchUserInfo?: () => Record<string, any>;
}> {
  const fetchUserInfo = async () => {
    try {
      const { data } = await currentUser();
      const { data: settings = {} } = await zabbix({
        method: 'settings.get',
      });
      const res = { ...data, settings };
      localStorage.setItem('RKLINK_SETTINGS', JSON.stringify(settings));
      return res;
    } catch (error) {
      console.log('🚗 🚗 🚗 ~ fetchUserInfo ~ error:', error);
    }
    return {};
  };
  // 如果不是登录页面，执行
  if (history.location.pathname !== loginPath) {
    const currentUser = await fetchUserInfo();
    return {
      currentUser,
      settings: defaultSettings,
    };
  }

  return {
    settings: defaultSettings,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  // const formRef = createRef<FormInstance>();

  return {
    breakpoint: 'xxl',
    rightContentRender: () => <RightContent />,
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 如果获取用户失败，提示重新登录
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        message.error('获取用户信息失败，请重新登录！');
      }
    },
    unAccessible: <UnAccessible />,
    token: {
      pageContainer: {
        paddingBlockPageContainerContent: 24,
        paddingInlinePageContainerContent: 24,
      },
      sider: {
        colorMenuBackground: '#fff',
        colorTextMenuSelected: '#13c2c2',
        colorTextMenuActive: '#13c2c2',
        colorTextMenuItemHover: '#13c2c2',
      },
    },
    onMenuHeaderClick: (e) => {
      if (!isMicroservice) {
        history.push('/');
        return;
      }
      // @ts-ignore
      if (e.target.nodeName === 'IMG') {
        window.open('/', '_self');
        return;
      }
      history.push('/');
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  timeout: 60000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  baseURL: BASE_URL,
  ...errorConfig,
};
