// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id查询建议 POST /api/v1/rump/alarm/find/by/${param0} */
export async function alarmFindById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.alarmFindByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultAlarmVO>(`/api/v1/rump/alarm/find/by/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取建议 POST /api/v1/rump/alarm/find/by/ids */
export async function alarmFindByIds(body: API.AlarmIdsRequest, options?: { [key: string]: any }) {
  return request<API.ResultListAlarmVO>('/api/v1/rump/alarm/find/by/ids', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
