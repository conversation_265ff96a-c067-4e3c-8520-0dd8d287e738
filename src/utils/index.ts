import { RequestOptionsType } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Route } from '@umijs/route-utils/dist/types';
import { message } from 'antd';
import { isMicroservice } from 'config/defaultSettings';
import * as CryptoJS from 'crypto-js';
import dayjs from 'dayjs';
import { isArray } from 'lodash';
import { Key, SyntheticEvent } from 'react';

const settingStr = localStorage.getItem('RKLINK_SETTINGS') || '{}';
const setting = JSON.parse(settingStr);

/**
 * 取消事件冒泡
 * @param e
 */
export function cancelBubble(e: SyntheticEvent) {
  e.stopPropagation();
  e.nativeEvent.stopImmediatePropagation();
}

export function renderEmpty(value: string | number | undefined) {
  return value ?? '-';
}

/**
 * Key[] 转antd option
 * @param arr Key[]
 * @returns option
 */

export const getOptions = (arr?: any[]): RequestOptionsType[] => {
  if (!arr || !arr.length) return [];
  return arr.map((item) => ({
    value: item,
    label: item,
  }));
};

/**
 * 获取表格参数
 * @param 表格参数
 * @returns 分页表格所需参数
 */

export const getTableParams = (params: Record<string, any>): BaseListRequest => {
  const { current, pageSize, restProps } = params;
  return {
    pageNum: current,
    pageSize,
    ...restProps,
  };
};

// 接口修改、新增成功 回调返回上一级
export const onSuccessAndGoBack = (res: any) => {
  if (res?.data) {
    message.success('操作成功！');
    history.go(-1);
  }
};

// option2enum
export const option2enum = (options: { value: React.Key; label: string }[]) => {
  const obj: Record<string, any> = {};
  options.forEach((item) => {
    const { value, label } = item;
    // @ts-ignore
    obj[value] = { text: label, ...item };
  });
  return obj;
};

/**
 * 字符串、int转时间
 * @param dateString、dateInt
 * @returns
 */

export const formatDate = (text: string | number) => {
  if (!text) return '-';
  return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 获取表格数据
 * @param params 参数
 * @param api 接口名称
 * @returns
 */

export const queryPagingTable = async <U>(
  params: { [k: string]: any },
  api?: (data: U) => Promise<Record<string, any>>,
) => {
  if (!api)
    return {
      data: [],
      success: true,
      total: 0,
    };
  const data = {
    output: 'extend',
    limit: Number(setting.search_limit),
    ...params,
  };
  const msg = await api(data as U);
  const result = msg?.data || [];
  return {
    data: result,
    success: true,
    total: result.length,
  };
};

export const queryRkPagingTable = async <U>(
  params: { [k: string]: any },
  api?: (data: U) => Promise<Record<string, any>>,
) => {
  if (!api)
    return {
      data: [],
      success: true,
      total: 0,
    };
  const { current, pageSize, filter, ...rest } = params;
  const data = {
    page: {
      page: current,
      size: pageSize,
    },
    filter,
    search: {
      ...rest,
    },
  };
  const msg = await api(data as U);
  const result = msg?.data;
  return {
    data: result?.data,
    success: true,
    total: result?.total,
  };
};

/**
 *  获取随机id
 */
export const getRandomId = () => {
  return (Math.random() * 1000000).toFixed(0);
};

/**
 * 获取options里的label
 * @param options 数组
 * @param val 值
 * @returns
 */
export const getOptionLabel = (options: Record<string, any>[] = [], val: Key) => {
  return options.find((item) => item.value === val)?.label || '';
};

/**
 * 获取下拉数据
 * @param params 参数
 * @param api 接口名称
 * @returns
 */

export const queryOptions = async <U>(
  params: { [k: string]: any },
  api?: (data: U, options?: Record<string, any>) => Promise<Record<string, any>>,
  options?: { [key: string]: any },
) => {
  if (!api) return [];
  const data = {
    output: 'extend',
    ...params,
  };
  const msg = await api(data as U, options);
  const result = msg?.data || [];
  return result;
};

/**
 * 获取form数据
 * @param params 参数
 * @param ready 是否请求
 * @param api 接口名称
 * @returns
 */

export const queryFormData = async <U>(
  params: { [k: string]: any },
  ready: boolean,
  api?: (data: U) => Promise<Record<string, any>>,
) => {
  if (!ready) return {};
  if (!api) return {};
  const msg = await api({ output: 'extend', ...(params as U) });
  const result = msg?.data?.[0] || {};
  return result;
};

/**
 * 数组转二进制
 * @param arr
 * @returns
 */

export const arrayToBinary = (arr: number[]) => {
  let binaryValue = 0;
  arr.forEach((option) => {
    binaryValue |= 1 << option;
  });

  return binaryValue;
};

/**
 * 将二进制转为选中的数组
 * @param allOptions 所有选项
 * @param value 二进制
 * @returns 选中的选项
 */

export const binaryToArray = (allOptions: Record<string, any>[] = [], value = '') => {
  const arr = allOptions.map(({ value }) => value);
  const binary = Number(value);
  return arr.filter((option) => (binary | (1 << option)) === binary);
};

/**
 * 检查值是否存在 为了 避开 0 和 false
 *
 * @param value
 */
export const checkUndefinedOrNull = (value: any) => value !== undefined && value !== null;

// 使用reduce方法将数组按照type分组

/**
 * 数组按照指定字段分组
 * @param arr 数组
 * @param groupingFields 数据分组的特定属性或字段
 */
export const groupedData = <T extends Record<string, any>>(arr: T[], groupingFields: string) => {
  return arr.reduce((result, current) => {
    const fields = current[groupingFields];
    if (!result[fields]) {
      result[fields] = [];
    }
    result[fields].push(current);
    return result;
  }, {} as Record<string, T[]>);
};

/**
 * 将查询参数转换为url
 * @param params 查询参数
 */

export const syncToUrl = (params: Record<string, string | string[]>) => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, val]) => {
    if (isArray(val)) {
      val.forEach((item) => {
        queryParams.append(key, item);
      });
    } else {
      queryParams.append(key, val);
    }
  });
  const pathname = window.location.pathname;
  const basePath = isMicroservice ? '/' + pathname.split('/').slice(2).join('/') : pathname;

  const searchUrl = queryParams.toString();
  history.push(`${basePath}?${searchUrl}`);
  window.localStorage.setItem(basePath, searchUrl);
};

export const setValuesToUndefined = (obj: Record<string, any>) => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      obj[key] = undefined;
    }
  }
  return obj;
};

/**
 * 携带参数跳转页面
 * @param pathname
 * @param params
 */
export const passParamsToPage = (pathname: string, params: Record<string, any>) => {
  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, val]) => {
    if (isArray(val)) {
      val.forEach((item) => {
        queryParams.append(key, item);
      });
    } else {
      queryParams.append(key, val);
    }
  });
  const searchUrl = queryParams.toString();
  window.localStorage.setItem(pathname, searchUrl);
  history.push(`${pathname}?${searchUrl}`);
};

/**
 * 获取小数点后位数
 * @param number
 * @returns
 */
export const countDecimalPlaces = (number: number) => {
  const decimalPart = (number.toString().split('.')[1] || '').length;
  return decimalPart;
};

/**
 * 以字节（B）为单位的数字值转换MB、GB、TB
 */
export const bytesToSize = (bytes: string | number) => {
  const val = Math.abs(Number(bytes));
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

  if (val === 0) return '0B';

  const i = parseInt(Math.floor(Math.log(val) / Math.log(1024)).toString());

  if (i === 0) return val + 'B';
  const res = val / Math.pow(1024, i);
  const pre = bytes > 0 ? '' : '-';
  return pre + (countDecimalPlaces(res) ? res.toFixed(2) : res) + sizes[i];
};

/**
 *  bps (比特每秒) 为单位的数装换为mbps (兆比特每秒) 或 gbps (千兆比特每秒)
 */
export const bpsToHumanReadable = (bps: string | number) => {
  const val = Math.abs(Number(bps));
  const units = ['bps', 'kbps', 'mbps', 'gbps', 'tbps'];
  if (bps === 0) return '0bps';

  const i = parseInt(Math.floor(Math.log(val) / Math.log(1000)).toString());
  const res = val / Math.pow(1000, i);
  const pre = bps > 0 ? '' : '-';
  return pre + (countDecimalPlaces(res) ? res.toFixed(2) : res) + units[i];
};

/**
 *  将以 Hz (赫兹) 为单位的数字值转换为更具可读性的 GHz (千兆赫兹) 或 MHz (兆赫兹)
 */
export const hzToHumanReadable = (hz: string | number) => {
  const val = Math.abs(Number(hz));
  const units = ['Hz', 'kHz', 'MHz', 'GHz', 'THz'];
  if (hz === 0) return '0Hz';

  const i = parseInt(Math.floor(Math.log(val) / Math.log(1000)).toString());
  const res = val / Math.pow(1000, i);
  const pre = hz > 0 ? '' : '-';
  return pre + (countDecimalPlaces(res) ? res.toFixed(2) : res) + units[i];
};

/**
 * 秒转时分秒
 * @param seconds
 * @returns
 */
export const formatSecondsToString = (seconds: number): string => {
  if (typeof seconds !== 'number' || isNaN(seconds)) return '';

  // Handle negative values
  const isNegative = seconds < 0;
  const absSeconds = Math.abs(seconds);

  const day = Math.floor(absSeconds / 3600 / 24);
  const hours = Math.floor((absSeconds / 3600) % 24);
  const minutes = Math.floor((absSeconds % 3600) / 60);
  const remainingSeconds = absSeconds % 60;

  const parts: string[] = [];

  if (day > 0) parts.push(`${day}天`);
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分`);
  parts.push(`${remainingSeconds}秒`);

  const formattedTime = parts.join('');

  return isNegative ? `-${formattedTime}` : formattedTime;
};

export function renderNumber(value?: number) {
  if (!value) return 0;
  return value;
}

// 接口修改、新增成功 刷新页面
export const onSuccessAndRefresh = (
  res: Record<string, any>,
  refresh?: ((delta?: number | undefined) => void) | any,
) => {
  if (res.code === 200) {
    message.success('保存成功！');
    refresh();
  }
};

// 操作成功
export const onSuccess = (res: Record<string, any>) => {
  if (res?.data) {
    message.success('操作成功！');
  }
};

/**
 * 日期范围转时间戳
 * @param dateRange 日期
 * @returns
 */
export const convertDateRangeToTimestamps = (
  dateRange: string[],
): { time_from?: number; time_till?: number } => {
  if (!dateRange || !dateRange?.length) return {};
  const [startDate, endDate] = dateRange;

  const startDateObj = dayjs(startDate);
  const endDateObj = dayjs(endDate);

  // 确保日期有效且起始日期不晚于结束日期
  if (!startDateObj.isValid() || !endDateObj.isValid() || startDateObj.isAfter(endDateObj)) {
    throw new Error('Invalid date range provided');
  }

  // 获取以秒为单位的 Unix 时间戳
  const time_from = startDateObj.unix();
  const time_till = endDateObj.unix();

  return { time_from, time_till };
};

export const encryptionKey = 'rkLink_login';

/**
 * 获取指定名称的cookie值。
 * @param name 需要获取的cookie的名称。
 * @returns 如果找到指定名称的cookie，则返回其值；如果找不到，则返回null。
 */
export const getCredentialsCookie = () => {
  const cookieArr = document.cookie.split('; ');

  for (let i = 0; i < cookieArr.length; i++) {
    const cookiePair = cookieArr[i].split('=');

    // 注意，如果Cookie没有值，它会以等号开始
    if (decodeURIComponent(cookiePair[0]) === 'credentials') {
      const info = JSON.parse(decodeURIComponent(cookiePair[1] || ''));

      try {
        const decryptedPassword = CryptoJS.AES.decrypt(info.password, encryptionKey).toString(
          CryptoJS.enc.Utf8,
        );
        info.password = decryptedPassword;
        return info;
      } catch (error) {
        console.error('Error decrypting cookie:', error);
        return null;
      }
    }
  }

  return null; // 如果找不到匹配的Cookie，返回null
};

/**
 * 根据路径查找对应的重定向路由
 *
 * 此函数旨在遍历给定的路由数组，寻找与指定路径匹配的路由对象如果找到，
 * 则返回该路由对象的重定向路径如果没有找到匹配的路由，则返回原始路径
 *
 * @param routes 路由数组，包含了多个路由对象，每个对象都有路径和重定向路径
 * @param path 待查找的路径，用于在路由数组中寻找匹配的路由对象
 * @returns 返回找到的重定向路径，如果没有找到匹配的路由，则返回原始路径
 */

export const findRedirectByPath = (routes: Route[], path: string): string | undefined => {
  for (const route of routes) {
    if (route.path === path) {
      return route.redirect;
    }
  }
  return path;
};

/**
 * 处理未授权的情况
 * 清除可能存在的登录状态标记，并引导用户前往登录页面
 **/

export const handleUnauthorized = () => {
  const loginPath = '/user/login';
  if (window.location.pathname.includes(loginPath)) return;
  history.push(loginPath);
  window.location.reload();
};

// 获取 tag 数据并去重分组

export const getTagData = (arr: Record<string, any>[]) => {
  const flatTags = arr.flatMap((item) => item.tags);
  const uniqueTags = [...new Set(flatTags.map((tag) => tag.tag))];

  return uniqueTags.map((tag) => {
    // 先筛选出所有 value，然后去重，再映射为对象
    const values = [
      ...new Set(flatTags.filter((item) => item.tag === tag).map((item) => item.value)),
    ];
    const children = values.map((value) => ({ value, label: value, tag }));
    return { title: tag, children };
  });
};
