import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { CONDITION_OPERATOR, CONDITION_TYPE, SEVERITIES, STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { useHostGroupList } from '@/hooks/useHostGroupList';
import { useHostList } from '@/hooks/useHostList';
import { useTemplateList } from '@/hooks/useTemplateList';
import { useTriggerList } from '@/hooks/useTriggerList';
import { useUserGroupList } from '@/hooks/useUserGroupList';
import { useUserList } from '@/hooks/useUserList';
import { zabbix, zabbixDelete, zabbixPost } from '@/services/zabbix';
import { getOptionLabel, option2enum, queryPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space, Typography } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
const { Text } = Typography;

const TriggerAction: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();
  const [selectedRows, setSelectedRows] = useState<RK_API.Action[]>([]);
  // 媒介类型
  const { data: mediaTypeList = [], loading: mediaTypeLoading } = useRequest(() =>
    zabbix({ method: 'mediatype.get' }),
  );

  // 用户
  const { userList, loading: userLoading } = useUserList();
  // 用户组
  const { userGroupList, loading: userGroupLoading } = useUserGroupList();
  // 对象群组
  const { hostGroupList, loading: hostGroupLoading } = useHostGroupList();
  // 主机列表
  const { hostList, loading: hostLoading } = useHostList();
  // 触发器列表
  const { triggerList } = useTriggerList();
  // 模版
  const { templateList } = useTemplateList();

  // 更新状态
  const { run: updateStatus } = useRequest(
    (params) =>
      zabbixPost({
        ...params,
        method: 'action.update',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('操作成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  // 删除
  const { run: deleteRecord } = useRequest(
    (ids) => zabbixDelete({ ids, method: 'action.delete' }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('删除成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  const handleDelete = async (rows: RK_API.Action[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.actionid!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除告警策略“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  const renderOpera = useCallback(
    (operations: RK_API.Operation[]) => {
      return operations.map((opera, operaIndex) => {
        const {
          operationtype,
          opmessage_grp = [],
          opmessage_usr = [],
          opmessage,
          opcommand,
          opcommand_hst = [],
          opcommand_grp = [],
        } = opera;
        // 发送信息
        if (operationtype === '0') {
          const arr = [...opmessage_usr, ...opmessage_grp];
          const mediaType = [{ mediatypeid: '0', name: '所有介质' }, ...mediaTypeList].find(
            (item) => item.mediatypeid === opmessage.mediatypeid,
          )?.name;
          return (
            <div key={operaIndex}>
              {arr.map((item, index) => {
                const str = item.userid ? '用户' : '用户组';
                const receiver = item.userid
                  ? userList?.find((user) => user.userid === item?.userid)?.username
                  : userGroupList?.find((group) => group.usrgrpid === item?.usrgrpid)?.name;
                return (
                  <div key={index}>
                    <Text strong>
                      通过{mediaType}发送信息给{str}：
                    </Text>
                    <Text>{receiver}</Text>
                  </div>
                );
              })}
            </div>
          );
        }
        // 脚本信息
        if (operationtype === '1') {
          const opera = opcommand?.scriptid === '1' ? '运行“Ping”脚本' : '运行“Traceroute”脚本';
          const arr = [...opcommand_hst, ...opcommand_grp];
          return (
            <div key={operaIndex}>
              {arr.map((item, index) => {
                const receiver = item.hostid
                  ? item?.hostid === '0'
                    ? '在当前主机上'
                    : `在主机${
                        hostList?.find((host: RK_API.Host) => host.hostid === item?.hostid)?.name
                      }`
                  : `在对象群组${
                      hostGroupList?.find(
                        (group: RK_API.HostGroup) => group.groupid === item?.groupid,
                      )?.name
                    }`;
                return (
                  <div key={index}>
                    <Text>{receiver}</Text>
                    <Text strong>{opera}</Text>
                  </div>
                );
              })}
            </div>
          );
        }
        return null;
      });
    },
    [userLoading, userGroupLoading, hostLoading, hostGroupLoading, mediaTypeLoading],
  );

  const renderCondition = (record: RK_API.Condition) => {
    const { conditiontype, operator, value, value2 } = record;
    const conditiontypeText = getOptionLabel(CONDITION_TYPE, conditiontype);
    const operatorText = getOptionLabel(CONDITION_OPERATOR, operator);
    // 问题被抑制
    if (conditiontype === '16')
      return <Text strong>{operator === '10' ? '问题被抑制' : '问题没有被抑制'}</Text>;
    // 触发器严重等级
    if (conditiontype === '4')
      return (
        <>
          <Text strong>{conditiontypeText}</Text>
          {`${operatorText}"${getOptionLabel(SEVERITIES, value)}"`}
        </>
      );
    if (value2)
      return (
        <>
          <Text strong>{conditiontypeText}</Text>
          {`${value2}${operatorText} ${value}`}
        </>
      );
    // 对象群组
    if (conditiontype === '0') {
      const hostGroup = hostGroupList.find(
        (item: RK_API.HostGroup) => item.groupid === value,
      )?.name;
      return (
        <>
          <Text strong>{conditiontypeText}</Text>
          {`${operatorText}"${hostGroup}"`}
        </>
      );
    }
    // 主机
    if (conditiontype === '1') {
      const host = hostList.find((item: RK_API.Host) => item.hostid === value)?.name;
      return (
        <>
          <Text strong>{conditiontypeText}</Text>
          {`${operatorText}"${host}"`}
        </>
      );
    }
    // 触发器
    if (conditiontype === '2') {
      const trigger = triggerList.find(
        (item: RK_API.Trigger) => item.triggerid === value,
      )?.description;

      return (
        <>
          <Text strong>{conditiontypeText}</Text>
          {`${operatorText}"${trigger}"`}
        </>
      );
    }
    // 模版
    if (conditiontype === '13') {
      const template = templateList.find(
        (item: RK_API.Template) => item.templateid === value,
      )?.name;
      return (
        <>
          <Text strong>{conditiontypeText}</Text>
          {`${operatorText}"${template}"`}
        </>
      );
    }
    return (
      <>
        <Text strong>{conditiontypeText}</Text>
        {`${operatorText}"${value}"`}
      </>
    );
  };

  // 表格
  const columns: ProColumns<RK_API.Action>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 220,
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              history.push(`/monitor-config/trigger-action/edit/${record.actionid}`);
            }}
          >
            {record.name}
          </a>
        );
      },
      initialValue: queryParams.get('name'),
    },
    {
      title: '条件',
      dataIndex: 'filter',
      hideInSearch: true,
      width: 400,
      render: (dom, entity) => {
        return entity?.filter?.conditions?.map((item, index) => (
          <div key={index}>{renderCondition(item)}</div>
        ));
      },
    },

    {
      title: '动作操作',
      dataIndex: 'operations',
      hideInSearch: true,
      width: 300,

      // @ts-ignore
      render: renderOpera,
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      ellipsis: true,
      valueEnum: option2enum(STATUS),
      initialValue: queryParams.get('status'),
    },
    {
      title: '操作',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        const { actionid } = record;
        return (
          <Space>
            {record.status === '1' && (
              <a
                key="enabled"
                onClick={() => {
                  updateStatus({
                    actionid,
                    status: 0,
                  });
                }}
              >
                启用
              </a>
            )}
            {record.status === '0' && (
              <a
                key="disabled"
                onClick={() => {
                  updateStatus({
                    actionid,
                    status: 1,
                  });
                }}
              >
                禁用
              </a>
            )}
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<RK_API.Action>
        {...defaultTableConfig}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        rowKey="actionid"
        formRef={formRef}
        actionRef={tableRef}
        columns={columns}
        headerTitle="告警策略"
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                history.push('/monitor-config/trigger-action/add');
              }}
            >
              新建动作
            </Button>,
          ],
        }}
        request={async (params) => {
          const { name, status } = params;
          return queryPagingTable<RK_API.Host>(
            {
              search: {
                name,
              },
              filter: {
                status,
                /**
                 * eventsource 可用值：
                    0 - 由触发器创建的事件；
                    1 - 由发现规则创建的事件；
                    2 - 主动式 agent 自动注册创建的事件；
                    3 - 内部事件；
                    4 - 在服务状态更新时创建的事件。
                 */
                eventsource: 0, //
              },
              selectOperations: 'extend',
              selectFilter: 'extend',
              sortfield: ['actionid', 'name'],
              method: 'action.get',
            },
            zabbix,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default TriggerAction;
