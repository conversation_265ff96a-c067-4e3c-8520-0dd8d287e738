import RKCol from '@/components/RKCol';
import { useHostGroupList } from '@/hooks/useHostGroupList';
import { getAllMonitoringObjectType } from '@/services/http/monitoringObject';
import { zabbix, zabbixPost } from '@/services/zabbix';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { IPReg, PortReg } from '@/utils/validator';
import {
  FooterToolbar,
  PageContainer,
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormItem,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Row } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import AgentSetter from './Agent';

// 保证每组 type 只有一个 main 为 '1'
function normalizeMain(list: readonly RK_API.Interface[]) {
  const grouped: Record<string, RK_API.Interface[]> = {};
  list.forEach((item) => {
    grouped[item.type] = grouped[item.type] || [];
    grouped[item.type].push(item);
  });
  return list.map((item) => {
    const group = grouped[item.type];
    // 该 type 的第一个就是 main=1，其余 main=0
    if (group[0] === item) {
      return { ...item, main: '1' };
    }
    return { ...item, main: '0' };
  });
}

const HostDetails: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  // 是否有类型
  const [hasType, setHasType] = useState(true);
  // 编辑模式下的macros数据，避免依赖导致的重新渲染
  const [editModeMacros, setEditModeMacros] = useState<any[]>([]);

  const { data: objectList = [], loading: objectListLoading } = useRequest(
    getAllMonitoringObjectType,
    {
      formatResult(res) {
        if (res?.code === 200) {
          return res?.data?.map((item) => ({
            value: item.id,
            label: item.name,
            options: item?.subTypes?.map((item) => ({
              value: item.id,
              label: item.name,
            })),
            ...item,
          }));
        }
        return [];
      },
    },
  );

  const { id, hostTypeId } = useParams();

  const isEditPage = !!id;

  const typeObj = useMemo(() => {
    return objectList?.flatMap?.((item) => item.subTypes)?.find((item) => item?.id === hostTypeId);
  }, [hostTypeId, objectList]);

  // 对象群组
  const { hostGroupList, loading: hostGroupLoading } = useHostGroupList();

  // 新建
  const { run: add, loading: addLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'host.create' }),

    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 修改
  const { run: update, loading: updateLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'host.update' }),

    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<RK_API.Host>
        formRef={formRef}
        initialValues={{
          status: '0',
          rkzl_monitorObjectSubtypeId: hostTypeId,
        }}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },

          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || updateLoading,
          },
        }}
        onFinish={async (values) => {
          const macros = values?.editMacros
            ? Object.keys(values?.editMacros)?.map((key) => {
                return {
                  id: key,
                  value: values?.editMacros[key],
                };
              })
            : values.macros;
          const interfaces = values?.interfaces || [];
          const normalized = normalizeMain(interfaces);

          const res = {
            ...values,
            editMacros: undefined,
            name: values.name || values.host,
            macros,
            interfaces: normalized,
            rkzl_monitorObjectSubtypeId: values?.rkzl_monitorObjectSubtypeId || typeObj?.id,
          };
          if (isEditPage) {
            update(res);
          } else {
            add(res);
          }
        }}
        request={async () => {
          const data = await queryFormData(
            {
              hostids: [id],
              selectTags: 'extend',
              selectGroups: 'extend',
              selectParentTemplates: 'extend',
              selectInterfaces: 'extend',
              selectMacros: 'extend',
              method: 'host.get',
            },
            isEditPage,
            zabbix,
          );
          data.interfaces =
            data?.interfaces?.filter((item: RK_API.Interface) => item?.ip && item?.type === '1') ||
            [];

          setHasType(!!data?.rkzl_subtype);

          // 在编辑模式下，保存macros数据到状态中
          if (isEditPage && data?.macros?.length) {
            setEditModeMacros(data.macros);
          }

          return data;
        }}
      >
        <div className="rk-none">
          <ProFormText name="hostid" />
          <ProFormText name="macros" />
        </div>
        {/* 基础设置 */}
        <ProCard
          title="基础信息"
          headStyle={{
            paddingInline: 0,
          }}
          bodyStyle={{
            paddingInline: 0,
          }}
        >
          <Row gutter={24}>
            <RKCol>
              <ProFormText label="主机名称" name="host" rules={[requiredRule]} />
            </RKCol>
            <ProFormDependency name={['host']}>
              {({ host }) => {
                return (
                  <RKCol>
                    <ProFormText label="可见的名称" placeholder={host} name="name" />
                  </RKCol>
                );
              }}
            </ProFormDependency>
            <RKCol>
              <ProFormSelect
                label="对象群组"
                name="groups"
                fieldProps={{
                  mode: 'multiple',
                  showSearch: true,
                  fieldNames: {
                    label: 'name',
                    value: 'groupid',
                  },
                  options: hostGroupList,
                  loading: hostGroupLoading,
                }}
                rules={[requiredRule]}
                convertValue={(value = []) => value.map((item: any) => item?.groupid)}
                getValueFromEvent={(value = []) => value.map((item: string) => ({ groupid: item }))}
                transform={(value: RK_API.HostGroup[], namePath) => ({
                  [namePath]: value.map(({ groupid }) => ({ groupid })),
                })}
              />
            </RKCol>
            <RKCol>
              <ProFormSwitch
                label="启用"
                name="status"
                getValueFromEvent={(val) => (val ? '0' : '1')}
                getValueProps={(value) => ({ checked: value === '0' })}
              />
            </RKCol>

            <ProFormDependency name={['hostid']}>
              {({ hostid }) => {
                if ((hostid && !hasType) || !hostid) {
                  return (
                    <RKCol>
                      <ProFormSelect
                        allowClear={false}
                        label="类型"
                        disabled={!!hostTypeId}
                        name="rkzl_monitorObjectSubtypeId"
                        fieldProps={{
                          options: objectList,
                          loading: objectListLoading,
                        }}
                        rules={[requiredRule]}
                      />
                    </RKCol>
                  );
                }
                return (
                  <RKCol>
                    <ProFormSelect
                      label="类型"
                      disabled
                      name="rkzl_monitorObjectSubtypeId"
                      fieldProps={{
                        options: objectList,
                        loading: objectListLoading,
                      }}
                      rules={[requiredRule]}
                    />
                  </RKCol>
                );
              }}
            </ProFormDependency>
            <RKCol lg={12} md={16} sm={24}>
              <ProFormTextArea
                label="描述"
                name="description"
                fieldProps={{
                  autoSize: { minRows: 1, maxRows: 6 },
                }}
              />
            </RKCol>
          </Row>
        </ProCard>
        {/* Agent 配置 */}
        <ProFormItem
          name="interfaces"
          rules={[
            {
              validator: (_, value) => {
                if (!value || value.length === 0) {
                  return Promise.reject(new Error('请添加至少一个接口'));
                }
                for (const item of value) {
                  if (item.type === '1') {
                    if (!item.ip || !item.port) {
                      return Promise.reject(new Error('IP 和端口不能为空'));
                    }
                    if (!IPReg.test(item.ip)) {
                      return Promise.reject(new Error('IP 格式不正确'));
                    }
                    if (!PortReg.test(item.port)) {
                      return Promise.reject(new Error('端口格式不正确'));
                    }
                  }
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <AgentSetter />
        </ProFormItem>
        {/* 通用变量 */}
        <ProCard
          title="通用变量"
          headStyle={{
            paddingInline: 0,
          }}
          bodyStyle={{
            paddingInline: 0,
          }}
        >
          {/* 编辑模式：使用稳定的状态数据，避免依赖导致的重新渲染 */}
          {isEditPage && editModeMacros?.length ? (
            <ProFormDependency name={['rkzl_subtype']}>
              {({ rkzl_subtype }) => {
                const object = objectList
                  ?.flatMap((item: any) => item.subTypes)
                  ?.find((item: any) => item?.id === rkzl_subtype?.id);

                return (
                  <Row gutter={24}>
                    {editModeMacros
                      ?.sort(
                        (a: Record<string, any>, b: Record<string, any>) =>
                          Number(a.id) - Number(b.id),
                      )
                      .map((item: Record<string, any>) => (
                        <RKCol key={`edit-${item.id}`}>
                          <ProFormText
                            label={object?.macros?.find((macro: any) => macro.id === item.id)?.name}
                            name={['editMacros', item.id!]}
                            initialValue={item.value}
                          />
                        </RKCol>
                      ))}
                  </Row>
                );
              }}
            </ProFormDependency>
          ) : !isEditPage ? (
            /* 新建模式：正常使用ProFormDependency */
            <ProFormDependency name={['rkzl_monitorObjectSubtypeId']}>
              {({ rkzl_monitorObjectSubtypeId }) => (
                <Row gutter={24}>
                  {objectList
                    ?.flatMap?.((item: any) => item.subTypes)
                    ?.find((item: any) => item?.id === rkzl_monitorObjectSubtypeId)
                    ?.macros?.map((item: any) => (
                      <RKCol key={`new-${item.id}`}>
                        <ProFormText label={item.name} name={['macros', item.id!]} />
                      </RKCol>
                    ))}
                </Row>
              )}
            </ProFormDependency>
          ) : null}
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default HostDetails;
