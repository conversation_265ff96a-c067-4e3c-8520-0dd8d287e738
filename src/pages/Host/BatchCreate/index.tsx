import { batchCreate, monitoringObjectExcelParse } from '@/services/http/monitoringObject';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { FooterToolbar, PageContainer, ProForm } from '@ant-design/pro-components';
import { history, useParams, useRequest } from '@umijs/max';
import { Button, message, Space, Table, Typography, Upload } from 'antd';
import React, { useState } from 'react';
import styles from './index.less';

const { Title, Text, Paragraph } = Typography;

const BatchCreateHost: React.FC = () => {
  const { hostTypeId } = useParams();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [tableData, setTableData] = useState<API.MonitorObjectExcelVO[]>([]);

  // 上传Excel文件解析的请求
  const { loading: uploadLoading, run: uploadExcel } = useRequest(
    (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('subTypeId', hostTypeId as string);
      return monitoringObjectExcelParse({} as any, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        requestType: 'form',
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result?.code === 200 && result?.data) {
          setTableData(result.data);
          message.success('文件解析成功！');
        } else {
          message.error(result?.message || '文件解析失败！');
        }
      },
      onError: (error) => {
        message.error('文件上传失败：' + error.message);
      },
      formatResult: (res) => res,
    },
  );

  // 批量创建的请求
  const { loading: saveLoading, run: saveBatchCreate } = useRequest(
    (data: API.MonitorObjectBatchCreateRequest[]) => {
      return batchCreate(data);
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result?.code === 200) {
          message.success('批量创建成功！');
          history.go(-1); // 返回上一页
        } else {
          message.error(result?.message || '批量创建失败！');
        }
      },
      formatResult: (res) => res,
    },
  );

  const columns = [
    {
      title: '主机',
      dataIndex: 'host',
      key: 'host',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '对象组',
      dataIndex: 'group',
      key: 'group',
    },
    {
      title: '是否启用',
      dataIndex: 'enable',
      key: 'enable',
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: '标签',
      dataIndex: 'tag',
      key: 'tag',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 文件选择处理
  const handleFileSelect = (file: File) => {
    // 检查文件类型
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      message.error('只能上传Excel格式的文件！');
      return false;
    }

    setSelectedFile(file);
    return false; // 阻止自动上传
  };

  // 导入按钮点击处理
  const handleImport = () => {
    if (!selectedFile) {
      message.warning('请先选择要导入的Excel文件！');
      return;
    }
    uploadExcel(selectedFile);
  };

  return (
    <PageContainer header={{ title: false }}>
      <ProForm
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },
          submitButtonProps: {
            disabled: tableData.length === 0,
            loading: saveLoading,
          },
          render: (_, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
        }}
        onFinish={async () => {
          if (tableData.length === 0) {
            message.warning('请先导入数据！');
            return false;
          }

          // 将表格数据转换为批量创建请求格式
          const batchCreateData: API.MonitorObjectBatchCreateRequest[] = tableData.map((item) => ({
            host: item.host,
            name: item.name,
            type: item.type,
            group: item.group,
            enable: item.enable,
            ip: item.ip,
            port: item.port,
            tag: item.tag,
            description: item.description,
          }));

          saveBatchCreate(batchCreateData);
          return true;
        }}
      >
        <Typography className={styles.batchCreateHost}>
          <Title level={4} className={styles.section}>
            <Space>
              <Text type="secondary" strong className={styles.stepNumber}>
                step1
              </Text>
              <span>下载模板</span>
            </Space>
          </Title>

          <Paragraph>
            点击“<strong className={styles.downloadTemplate}>下载模板</strong>
            ”获取标准的信息导入模板。
          </Paragraph>
          <Paragraph>
            请根据实际情况完整、准确地填写模板中的每一项内容。模板中包含所有必填字段（如名称、对象组、类型等），并提供了示例数据供参考。
          </Paragraph>
          <Paragraph className={styles.section}>
            填写完成后，请确认保存文件为 Excel 格式（.xlsx），并点击<strong>“导入”</strong>
            按钮进行上传。
          </Paragraph>

          <Paragraph className={styles.section}>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => {
                window.open('/api/v1/rump/monitoring-object/download/template/' + hostTypeId);
              }}
            >
              下载模板
            </Button>
          </Paragraph>

          <Title level={4} className={styles.section}>
            <Space>
              <Text type="secondary" strong className={styles.stepNumber}>
                step2
              </Text>
              <span>批量导入</span>
            </Space>
          </Title>

          <Paragraph>
            <Text>请选择导入模板：</Text>
            <Upload
              accept=".xlsx,.xls"
              beforeUpload={handleFileSelect}
              showUploadList={false}
              maxCount={1}
            >
              <Button>选择文件</Button>
            </Upload>
            {selectedFile && (
              <div style={{ margin: '20px 0', color: 'var(--primary-color)' }}>
                已选择：{selectedFile.name}
              </div>
            )}
          </Paragraph>

          <Paragraph>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={handleImport}
              loading={uploadLoading}
              disabled={!selectedFile}
            >
              导入
            </Button>
          </Paragraph>

          <Table
            size="middle"
            columns={columns}
            dataSource={tableData}
            rowKey={(record, index) => `${record.name}-${index}`}
            pagination={false}
          />
        </Typography>
      </ProForm>
    </PageContainer>
  );
};

export default BatchCreateHost;
