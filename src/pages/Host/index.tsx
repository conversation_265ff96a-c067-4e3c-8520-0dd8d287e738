import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import React, { Key, useMemo, useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { INTERFACE_TYPE, STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import useExportApi from '@/hooks/useExportApi';
import { zabbix, zabbixDelete, zabbixList, zabbixPost } from '@/services/zabbix';
import {
  getTagData,
  groupedData,
  option2enum,
  passParamsToPage,
  queryPagingTable,
  syncToUrl,
} from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { history, Link, useRequest } from '@umijs/max';
import {
  Button,
  Empty,
  Input,
  message,
  Modal,
  Space,
  Spin,
  Switch,
  Tag,
  Tree,
  Typography,
} from 'antd';
import InterfaceModal from './components/InterfaceModal';

import BaseContext from '@/Context/BaseContext';
import { getAllMonitoringObjectType } from '@/services/http/monitoringObject';
import HostGroupModalForm from '../HostGroup/components/HostGroupModalForm';
import { valueTypeMap } from '../HostMonitor/valueTypeMap';
import CreateTypeModal from './components/CreateTypeModal';
import Item from './components/Item';
import styles from './index.less';

const actions = [
  {
    key: 'export',
    label: '导出',
  },
];

export const getColor = (arr: RK_API.Interface[]) => {
  const availableArr = arr.map((item) => item.available);
  // '0' 未知   '1' - 可用  '2' - 不可用
  // 至少有一个未知的接口(无不可用)
  if (availableArr.includes('0')) {
    return '';
  }
  // 没有可用的接口
  if (!availableArr.some((item) => item === '1')) {
    return 'red';
  }
  // 所有接口可用
  if (availableArr.every((item) => item === '1')) {
    return '#87d068';
  }
  if (availableArr.includes('0')) return '';
  return 'gold';
};
const Host: React.FC = withStorageToUrl(({ queryParams }) => {
  const actionRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();
  const groupIdRef = useRef<Key[]>([]);
  const groupInitialValuesRef = useRef<RK_API.HostGroup>();

  // 获取所有对象
  const { data: objectList = [], loading: objectListLoading } = useRequest(
    getAllMonitoringObjectType,
    {
      formatResult(res) {
        if (res?.code === 200) {
          return res?.data?.map((item) => ({
            value: item.id,
            label: item.name,
            options: item?.subTypes?.map((item) => ({
              value: item.id,
              label: item.name,
            })),
            ...item,
          }));
        }
        return [];
      },
    },
  );

  const [modalVisit, setModalVisit] = useState(false);
  const [groupModalVisit, setGroupModalVisit] = useState(false);
  const interfaceArr = useRef<{ interfaces: RK_API.Interface[] }>();
  const [selectedRows, setSelectedRows] = useState<RK_API.Host[]>([]);

  const [keywords, setKeywords] = useState<string>('');
  // 获取主机组
  const {
    data: groupData = [],
    loading,
    refresh,
  } = useRequest(() => zabbixList({ method: 'hostgroup.get', output: ['name', 'groupid'] }));

  const treeData = useMemo(() => {
    const arr = groupData?.filter((item: RK_API.HostGroup) =>
      item.name?.toLocaleLowerCase()?.includes(keywords.toLocaleLowerCase()),
    );
    if (!groupData.length || !arr.length) return [];
    return [
      {
        key: 'all',
        title: <span>全部</span>,
        children: arr?.map((item: RK_API.HostGroup) => ({
          key: item.groupid,
          title: (
            <Item
              name={item.name}
              groupid={item.groupid}
              onEdit={() => {
                groupInitialValuesRef.current = item;
                setGroupModalVisit(true);
              }}
              onFinish={refresh}
            />
          ),
        })),
      },
    ];
  }, [groupData, keywords]);

  // 返回所有监控对象的标签
  const { data: hostTags } = useRequest(
    () => zabbix({ method: 'host.get', output: ['tags'], selectTags: 'extend' }),
    {
      formatResult: (res) => {
        if (res.code === 200) {
          return getTagData(res.data);
        }
      },
    },
  );

  const onSelect = (keys: Key[]) => {
    groupIdRef.current = keys;
    formRef.current?.submit();
  };

  // 获取监控对象列表
  const { run: getHosts } = useRequest((params) => zabbixList(params), {
    manual: true,
  });

  // 删除
  const { run: deleteRecord } = useRequest((ids) => zabbixDelete({ ids, method: 'host.delete' }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      actionRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: RK_API.Host[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.hostid!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除监控对象“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 更新状态
  const { run: updateStatus, fetches: updateStatusFetches } = useRequest(
    (params) =>
      zabbixPost({
        ...params,
        method: 'host.update',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        actionRef.current?.reloadAndRest?.();
        if (res?.code !== 200) return;
        message.success('操作成功');
      },

      formatResult: (res) => res,
      fetchKey: (params) => params.hostid,
    },
  );

  // 导出
  const { run: exportTemplate } = useExportApi();
  const onOperation = (key: string | number, selectedRows: RK_API.Host[]) => {
    const ids = selectedRows.map((item) => item.hostid!);
    if (key === 'export') {
      exportTemplate({
        options: {
          hosts: ids,
        },
        fileName: 'rkmon_host',
        format: 'xml',
      });
    }
  };

  // 表格
  const columns: ProColumns<RK_API.Host>[] = [
    {
      dataIndex: 'groupId',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
      initialValue: queryParams.get('name'),
      // copyable: true,
      fixed: 'left',
      render: (dom, record) => {
        return <Link to={`/monitor-config/host/details/${record.hostid}`}>{dom}</Link>;
      },
    },

    {
      dataIndex: 'groups',
      title: '对象组',
      hideInSearch: true,
      render: (_, record) => {
        return record.groups.map((item: RK_API.HostGroup) => item.name).join(',');
      },
    },
    {
      dataIndex: 'rkzl_monitorObjectSubtypeId',
      title: '类型',
      width: 120,
      valueType: 'select',
      fieldProps: {
        options: objectList,
        loading: objectListLoading,
      },
      hideInTable: true,
    },
    {
      dataIndex: ['rkzl_type', 'name'],
      title: '类型',
      width: 120,
      hideInSearch: true,
    },
    {
      dataIndex: ['rkzl_subtype', 'name'],
      title: '子类型',
      width: 100,
      hideInSearch: true,
    },
    // { ...template, initialValue: queryParams.getAll('templateids') },
    // {
    //   title: '监控项',
    //   width: 80,
    //   dataIndex: 'items',
    //   hideInSearch: true,
    //   render(dom, entity) {
    //     const { items = [] } = entity;
    //     return (
    //       <a
    // onClick={() =>
    //   passParamsToPage('/monitor-config/host/monitor-item', {
    //     hostids: entity.hostid,
    //   })
    // }
    //       >
    //         {items.length ? `${items.length}项` : '监控项'}
    //       </a>
    //     );
    //   },
    // },
    // {
    //   title: '触发器',
    //   width: 80,
    //   dataIndex: 'triggers',
    //   hideInSearch: true,
    //   render(dom, entity) {
    //     const { triggers = [] } = entity;
    //     return (
    //       <a
    //         onClick={() =>
    //           passParamsToPage('/monitor-config/host/trigger', { hostids: entity.hostid })
    //         }
    //       >
    //         {triggers.length ? `${triggers.length}项` : '触发器'}
    //       </a>
    //     );
    //   },
    // },
    // TODO
    // {
    //   title: '自动发现',
    //   width: 80,
    //   dataIndex: 'discoveries',
    //   hideInSearch: true,
    //   render(dom, entity) {
    //     const { discoveries = [] } = entity;
    //     return <a>{discoveries.length ? `${discoveries.length}项` : '自动发现'}</a>;
    //   },
    // },
    // {
    //   title: 'agent代理程序',
    //   dataIndex: '1',
    //   width: 150,
    //   formItemProps: {
    //     className: 'form-label-warp',
    //   },
    // },
    // {
    //   title: '监控模板',
    //   dataIndex: 'parentTemplates',
    //   width: 200,
    //   hideInSearch: true,
    //   render(dom, record) {
    //     const { parentTemplates = [] } = record;
    //     if (!parentTemplates.length) return '';
    //     return parentTemplates.map((item: RK_API.TemplateGetDTO, index: number) => (
    //       <React.Fragment key={item?.templateid}>
    //         {index > 0 && <a>,</a>}
    //         {/* <Button
    //           type="link"
    //           onClick={() => {
    //             history.push(`/monitor-config/template/edit/${item?.templateid}`);
    //           }}
    //         >
    //         </Button> */}
    //         {item?.name}
    //       </React.Fragment>
    //     ));
    //   },
    // },
    {
      title: '标签',
      dataIndex: 'rk_tags',
      width: 200,

      // @ts-ignore
      valueType: 'tagsSelectSetter',
      fieldProps: {
        tags: hostTags,
      },
      render(_, entity) {
        const { tags = [] } = entity;

        if (!tags.length) return <>-</>;
        return (tags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      ellipsis: true,
      valueEnum: option2enum(STATUS),

      render(_, entity) {
        const checked = entity.status === '0';

        return (
          <Switch
            checked={checked}
            loading={updateStatusFetches?.[entity.hostid!]?.loading}
            onChange={() => {
              updateStatus({
                hostid: entity.hostid,
                status: checked ? 1 : 0,
              });
            }}
          />
        );
      },
    },
    {
      title: '可用性',
      width: 150,
      dataIndex: 'interfaces',
      hideInSearch: true,
      tooltip: '点击tag查看接口详情',
      render(dom, entity) {
        const { interfaces = [] } = entity;

        const data = groupedData(interfaces, 'type');
        return Object.entries(data).map(([type, arr]) => {
          const color = getColor(arr);
          return (
            <Tag
              style={{
                cursor: 'pointer',
              }}
              key={arr?.[0].interfaceid}
              color={color}
              onClick={() => {
                setModalVisit(true);
                interfaceArr.current = { interfaces: arr };
              }}
            >
              {option2enum(INTERFACE_TYPE)[type]?.text}
            </Tag>
          );
        });
      },
    },

    // {
    //   title: 'agent 加密',
    //   dataIndex: 'tls_connect',
    //   width: 160,
    //   hideInSearch: true,
    //   valueEnum: option2enum(TLS_CONNECT),
    // },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
    },

    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      width: 250,
      render: (text, record) => {
        const { hostid } = record;

        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                history.push(`/monitor-config/host/edit/${hostid}`);
              }}
            >
              编辑
            </Button>

            <Button
              type="link"
              key="metrics"
              onClick={() => passParamsToPage(`/monitor-config/host/monitor-item/${hostid}`, {})}
            >
              指标配置
            </Button>

            <Button
              type="link"
              key="trigger"
              onClick={() => passParamsToPage(`/monitor-config/host/trigger/${hostid}`, {})}
            >
              阈值设置
            </Button>
            <Button type="link" onClick={() => handleDelete([record])}>
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <BaseContext.Provider value={{ objectList, objectListLoading }}>
          <ProTable<RK_API.Host>
            {...defaultTableConfig}
            formRef={formRef}
            rowKey="hostid"
            search={SearchOptionRender}
            onSubmit={syncToUrl}
            actionRef={actionRef}
            rowSelection={{
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRows(selectedRows);
              },
            }}
            tableRender={(_, dom) => (
              <div className={styles['table-warp']}>
                <div className={styles['left']}>
                  <div className={styles['header']}>
                    <Typography.Title level={5} className={styles['title']}>
                      监控对象组
                      <Button
                        type="link"
                        onClick={() => {
                          groupInitialValuesRef.current = {};
                          setGroupModalVisit(true);
                        }}
                      >
                        <PlusOutlined />
                        新增
                      </Button>
                    </Typography.Title>
                    <Input
                      placeholder="请输入对象组名称"
                      allowClear
                      suffix={<SearchOutlined />}
                      onPressEnter={(e) => {
                        setKeywords((e.target as HTMLInputElement).value);
                      }}
                      onChange={(e) => {
                        const val = (e.target as HTMLInputElement).value;
                        if (!val.length) setKeywords('');
                      }}
                    />
                  </div>
                  <div className={styles.content}>
                    <Spin spinning={loading} className={styles.tree}>
                      {treeData.length ? (
                        <Tree.DirectoryTree
                          className={styles['tree']}
                          blockNode
                          defaultExpandedKeys={['all']}
                          defaultSelectedKeys={['all']}
                          treeData={treeData}
                          onSelect={onSelect}
                          showIcon={false}
                          expandAction={'doubleClick'}
                        />
                      ) : (
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                      )}
                    </Spin>
                  </div>
                </div>
                <div className={styles['table-content']}>{dom}</div>
              </div>
            )}
            columns={columns}
            headerTitle="监控对象列表"
            size="small"
            toolbar={{
              actions: [
                // <ImportButton
                //   key="import"
                //   params={{
                //     format: 'xml',
                //     rules: {
                //       hosts: {
                //         createMissing: true,
                //         updateExisting: true,
                //       },
                //     },
                //   }}
                // />,

                <CreateTypeModal key="create" />,
              ],
            }}
            request={async (params) => {
              const {
                name,
                groupids,
                templateids,
                parentTemplateids,
                rkzl_monitorObjectSubtypeId,
                status,
                rk_tags,
              } = params;

              const res = await queryPagingTable<RK_API.Host>(
                {
                  search: {
                    name,
                  },
                  tags: rk_tags,
                  filter: {
                    rkzl_monitorObjectSubtypeId,
                    status,
                  },
                  groupids:
                    groupIdRef.current.includes('all') || !groupIdRef.current.length
                      ? null
                      : groupIdRef.current,
                  templateids: templateids?.length ? templateids : null,
                  parentTemplateids: parentTemplateids?.length ? parentTemplateids : null,

                  sortfield: 'name',
                  method: 'host.get',
                  output: ['hostid', 'name'],
                },
                zabbixList,
              );
              // 获取所有host id
              const hostIds = res.data?.map((item: RK_API.Item) => item.hostid) || [];
              const { current = 1, pageSize = 10 } = params;
              const index = (current - 1) * pageSize;

              const hostList = await getHosts({
                search: {
                  name,
                },

                groupids: groupids?.length ? groupids : null,
                templateids: templateids?.length ? templateids : null,
                parentTemplateids: parentTemplateids?.length ? parentTemplateids : null,
                selectItems: 'extend',
                selectTriggers: 'extend',
                selectDiscoveries: 'extend',
                selectInterfaces: 'extend',
                selectParentTemplates: 'extend',
                selectGroups: 'extend',
                selectTags: 'extend',

                sortfield: 'name',
                hostids: hostIds.splice(index, pageSize),
                method: 'host.get',
              });
              return {
                ...res,
                data: hostList,
              };
            }}
            scroll={{ x: 'max-content', y: 640 }}
          />
        </BaseContext.Provider>
        <OperateFooterToolbar
          selectedRows={selectedRows}
          onDelete={handleDelete}
          actions={actions}
          onOperation={onOperation}
        />
        <InterfaceModal
          onOpenChange={setModalVisit}
          open={modalVisit}
          initialValues={interfaceArr.current}
        />

        {/* 新建/编辑 */}
        <HostGroupModalForm
          initialValues={groupInitialValuesRef.current}
          open={groupModalVisit}
          onOpenChange={(visible) => {
            setGroupModalVisit(visible);
          }}
          onFinish={async () => {
            refresh();
          }}
        />
      </ProConfigProvider>
    </PageContainer>
  );
});

export default Host;
