import { PROBLEMS_SHOW, SEVERITIES, SUPPRESSED_STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { alarmFindByIds } from '@/services/http/alarm';
import { zabbix } from '@/services/zabbix';
import { formatSecondsToString, option2enum, queryPagingTable, syncToUrl } from '@/utils';
import { host, hostGroups } from '@/utils/column';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { useUpdate } from 'ahooks';
import { Badge, Space, Tag, Tooltip, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useMemo, useRef, useState } from 'react';
import { valueTypeMap } from '../HostMonitor/valueTypeMap';
import ActionModal from './components/ActionModal';
import MarkdownModal from './components/MarkdownModal';
import UpdateDrawer from './components/UpdateDrawer';
// const dateType = [
//   {
//     type: 'day',
//     name: '本日',
//   },
//   {
//     type: 'isoWeek',
//     name: '本周',
//   },
//   {
//     type: 'month',
//     name: '本月',
//   },
//   {
//     type: 'month',
//     name: '近三个月',
//     num: 3,
//   },
//   {
//     type: 'month',
//     name: '近半年',
//     num: 6,
//   },
// ];
// const ExtraFooter = useCallback(
//   () => (
//     <Space size={16} style={{ marginLeft: 24 }}>
//       {dateType?.map((item, index) => (
//         <a key={index} onClick={() => onSelectDate(item.type, item?.num)}>
//           {item.name}
//         </a>
//       ))}
//     </Space>
//   ),
//   [],
// );
// const onSelectDate = useCallback((dateType: any, num?: number) => {
//   let dateArr = [];
//   if (num) {
//     // 获取当前日期
//     const currentDate = dayjs();
//     const startDate = currentDate.subtract(num, dateType);
//     dateArr = [startDate, currentDate];
//   } else {
//     dateArr = [dayjs().startOf(dateType), dayjs().endOf(dateType)];
//   }
//   formRef.current?.setFieldValue('date', dateArr);
// }, []);

const Alarm: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();
  const [modalVisit, setModalVisit] = useState(false);
  const [markdownModalVisit, setMarkdownModalVisit] = useState(false);
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [content, setContent] = useState('');
  const actionRef = useRef<any[]>();
  const problemRef = useRef<RK_API.Problem>();
  const recoverRef = useRef('2');
  const update = useUpdate();

  if (queryParams.get('showStyle') === null) {
    formRef.current?.setFieldValue('showStyle', '0');
  }

  const timestampArr = useMemo(() => {
    const dateArr = queryParams.getAll('date');

    if (!queryParams.get('showStyle')) return [];

    if (!dateArr.length) return [];
    const [startDate, endDate] = dateArr;
    return [dayjs(startDate).startOf('D'), dayjs(endDate).endOf('D')];
  }, [queryParams.getAll('date')]);

  // 获取trigger
  const { run: getTriggers, data: triggers } = useRequest(
    (triggerids) =>
      zabbix({
        method: 'trigger.get',
        triggerids,
        monitored: '1',
        skipDependent: '1', // 跳过处于问题状态且依赖于其他触发器的触发器
        preservekeys: '1',
        selectHosts: 'extend',
        selectFilter: 'extend',
        lastChangeSince: timestampArr.at(0)?.unix(),
        lastChangeTill: timestampArr.at(1)?.unix(),
        output: ['hosts', 'manual_close', 'lastchange', 'comments'],
      }),
    {
      manual: true,
    },
  );

  // 获取告警信息
  const { run: getAlert, data: alertArr = [] } = useRequest(
    (eventids) =>
      zabbix({
        method: 'alert.get',
        selectUsers: 'extend',
        selectMediatypes: 'extend',
        eventids,
        time_from: timestampArr.at(0)?.unix(),
        time_till: timestampArr.at(1)?.unix(),
      }),
    {
      manual: true,
      onSuccess: () => {
        update();
      },
    },
  );

  // 获取已解决事件的信息
  const { run: getResolvedEvents, data: resolvedEvents = {} } = useRequest(
    (eventids) =>
      zabbix({
        method: 'event.get',
        eventids,
        output: ['eventid', 'clock'],
        preservekeys: '1',
      }),
    {
      manual: true,
    },
  );

  // 获取AI分析
  const { run: getAI } = useRequest((ids) => alarmFindByIds({ ids }), {
    manual: true,
    onSuccess: (data) => {
      setContent(data?.[0]?.suggestion?.suggestion || '');
      setMarkdownModalVisit(true);
    },
  });

  const columns: ProColumns<RK_API.Problem>[] = [
    {
      title: '显示',
      colSize: 4,
      dataIndex: 'showStyle',
      hideInTable: true,
      valueType: 'radio',
      fieldProps: {
        options: PROBLEMS_SHOW,
        optionType: 'button',
        buttonStyle: 'solid',
        onChange(e) {
          if (e.target.value === '0') {
            formRef.current?.setFieldValue('date', []);
          } else {
            formRef.current?.setFieldValue('recover', recoverRef.current || '2');
          }
          setTimeout(() => {
            formRef.current?.submit();
          }, 500);
        },
      },
      initialValue: queryParams.get('showStyle') || '0',
    },
    {
      title: '时间',
      dataIndex: 'clock',
      hideInSearch: true,
      renderText(text) {
        return dayjs.unix(text).format('YYYY-MM-DD HH:mm:ss');
        // return dayjs.unix(triggers?.[record.objectid]?.lastchange).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '恢复时间',
      dataIndex: 'r_eventid',
      hideInSearch: true,
      hideInTable: queryParams.get('showStyle') !== '1',
      renderText(text) {
        if (text === '0') return '-';
        return (
          resolvedEvents?.[text] &&
          dayjs.unix(resolvedEvents?.[text].clock).format('YYYY-MM-DD HH:mm:ss')
        );
      },
    },
    {
      title: '问题',
      dataIndex: 'name',
      hideInTable: true,
      initialValue: queryParams.get('name'),
    },
    {
      title: '严重性',
      dataIndex: 'severity',
      valueEnum: option2enum(SEVERITIES),
      render: (text, record) => {
        if (!text) return text;
        const severityObj = SEVERITIES.find((item) => record.severity === item.value);
        return severityObj ? (
          <Tooltip title={severityObj.label}>
            <Tag color={severityObj.tagColor}>{text}</Tag>
          </Tooltip>
        ) : (
          text
        );
      },
      hideInSearch: true,
    },

    {
      ...hostGroups,
      initialValue: queryParams.getAll('groupids'),
    },
    {
      ...host,
      initialValue: queryParams.getAll('hostids'),
    },
    {
      title: '时间范围',
      dataIndex: 'date',
      hideInTable: true,
      valueType: 'dateRange',
      initialValue: timestampArr,
      fieldProps: () => ({
        presets: [
          { label: '本日', value: [dayjs(), dayjs()] },
          // @ts-ignore
          { label: '本周', value: [dayjs().startOf('isoWeek'), dayjs().endOf('isoWeek')] },
          { label: '本月', value: [dayjs().startOf('M'), dayjs().endOf('M')] },
          { label: '近三个月', value: [dayjs().subtract(3, 'month'), dayjs()] },
          { label: '近半年', value: [dayjs().subtract(6, 'month'), dayjs()] },
        ],
        // disabled: form.getFieldValue('showStyle') !== '1',
        // defaultValue: timestampArr,
      }),
      // hideInSearch: queryParams.get('showStyle') !== '1',
    },
    {
      title: '严重性',
      dataIndex: 'severities',
      valueType: 'checkbox',
      colSize: 2,
      fieldProps: {
        options: SEVERITIES,
      },
      hideInTable: true,
      initialValue: queryParams.getAll('severities'),
    },
    {
      title: '状态',
      dataIndex: 'suppressed',
      width: 100,
      hideInSearch: true,
      valueEnum: option2enum(SUPPRESSED_STATUS),
      render: (dom, entity) => {
        if (Number(entity?.r_eventid) > 0) {
          return <Badge status="success" text="已解决" />;
        }
        return dom;
      },
    },
    {
      title: '主机',
      dataIndex: 'objectid',
      hideInSearch: true,
      renderText: (objectId, entity) => {
        // @ts-ignore
        const obj = entity?.hosts?.length ? entity : triggers?.[objectId];
        return obj?.hosts?.at(0)?.name;
      },
      width: 180,
    },
    {
      title: '问题',
      dataIndex: 'name',
      width: 350,
      hideInSearch: true,
      render(dom, entity) {
        return (
          <Tooltip
            // placement="left"
            title={
              triggers[entity?.objectid]?.comments && (
                <Typography.Text copyable>{triggers[entity?.objectid]?.comments}</Typography.Text>
              )
            }
          >
            {dom}
          </Tooltip>
        );
      },
    },

    {
      title: '持续时间',
      dataIndex: 'clock',
      hideInSearch: true,
      width: 200,
      renderText(text, entity) {
        if (entity?.r_eventid === '0') {
          const now = dayjs().unix();
          // 未解决的告警，使用当前时间减去告警开始时间
          const seconds = now - text;
          return formatSecondsToString(seconds);
        } else {
          const r_time = resolvedEvents?.[entity?.r_eventid]?.clock;
          const seconds = r_time - text;
          return formatSecondsToString(seconds);
        }
      },
    },
    {
      title: '确认问题',
      dataIndex: 'acknowledged',
      hideInSearch: true,
      width: 100,
      renderText(text) {
        return text === '1' ? '是' : '否';
      },
    },
    {
      title: '动作',
      dataIndex: 'eventid',
      hideInSearch: true,
      width: 100,
      render: (id, { acknowledges = [] }) => {
        if (!alertArr?.length) return '';
        const action = alertArr?.filter((item: RK_API.Alert) => item.eventid === id);

        const arr = [...action, ...acknowledges].sort((a, b) => b.clock - a.clock);
        return (
          <a
            onClick={() => {
              setModalVisit(true);
              actionRef.current = arr;
            }}
          >
            {arr?.length}步
          </a>
        );
      },
    },
    {
      title: '告警情况',
      dataIndex: 'recover',
      valueType: 'radio',
      hideInTable: true,
      fieldProps: {
        options: [
          { label: '全部', value: '2' },
          { label: '已恢复', value: '1' },
          { label: '未恢复', value: '0' },
        ],
        onChange: (e) => {
          recoverRef.current = e.target.value;
        },
      },
      colSize: 2,
      initialValue: queryParams.get('recover') || '2',
      hideInSearch: queryParams.get('showStyle') !== '1',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      colSize: 3,
      // @ts-ignore
      valueType: 'conditionSetter',
      search: {
        transform: (value) => ({
          tags: value?.conditionList || [],
          evaltype: value?.evaltype,
        }),
      },
      render(dom, entity) {
        const { tags = [], inheritedTags = [] } = entity;
        const mixTags = [...inheritedTags, ...tags];
        if (!mixTags.length) return <>-</>;
        return (mixTags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
    {
      title: '操作',
      width: 130,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        const hasDeepSeek = record?.tags?.find(
          (item: RK_API.TemplateTag) => item.tag === 'rkmview_gjsj_ai' && item.value === 'deepseek',
        );

        return (
          <Space>
            {hasDeepSeek && (
              <a
                onClick={() => {
                  getAI([record.eventid]);
                }}
              >
                AI分析
              </a>
            )}
            <a
              onClick={() => {
                setDrawerVisit(true);
                problemRef.current = record;
              }}
            >
              确认
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <ProTable<RK_API.Problem>
          {...defaultTableConfig}
          search={SearchOptionRender}
          onSubmit={syncToUrl}
          rowKey="eventid"
          formRef={formRef}
          actionRef={tableRef}
          columns={columns}
          headerTitle="告警列表"
          request={async (params) => {
            const {
              name,
              groupids = [],
              hostids = [],
              evaltype,
              tags = [],
              severities = [],
              showStyle = '0',
              recover = recoverRef.current || '2',
              current = 1,
              pageSize = 10,
            } = params;
            const index = (current - 1) * pageSize;
            const problem = await queryPagingTable<RK_API.Trigger>(
              {
                search: {
                  name,
                },
                source: 0,
                object: 0,
                recent: false,
                severities: severities.length ? severities : null,
                groupids: groupids?.length ? groupids : null,
                hostids: hostids?.length ? hostids : null,
                evaltype,
                tags: tags.filter((item: RK_API.TemplateTag) => item.tag),
                selectTags: 'extend',
                selectAcknowledges: 'extend',
                suppressed: false,
                // selectHosts: 'extend',
                value: showStyle === '1' ? '1' : undefined,
                select_acknowledges: showStyle === '1' ? 'extend' : undefined,
                method: showStyle === '0' ? 'problem.get' : 'event.get',
                time_from: timestampArr?.at(0)?.unix(),
                time_till: timestampArr?.at(1)?.unix(),
                sortfield: showStyle === '0' ? 'eventid' : 'clock',
                sortorder: ['DESC'],
                output: [
                  'eventid',
                  'severity',
                  'objectid',
                  'name',
                  'suppressed',
                  'clock',
                  'r_eventid',
                  'acknowledged',
                ],
              },
              zabbix,
            );
            const arr = problem.data?.map((item: RK_API.Problem) => item.objectid);
            // 使用 Set 来去重
            const triggerIds = [...new Set(arr)];
            const res = await getTriggers(triggerIds);
            // 不要依赖于其他触发器的触发
            const filterKeys = Object.keys(res || []);

            let resultData = problem?.data?.filter((item: RK_API.Problem) =>
              filterKeys.includes(item.objectid),
            );

            // 首页跳转到此页面数据筛选

            if (showStyle === '1' && recover === '1') {
              resultData = resultData.filter((item: RK_API.Problem) => Number(item.r_eventid) > 0);
            }

            if (showStyle === '1' && recover === '0') {
              resultData = resultData.filter((item: RK_API.Problem) => Number(item.r_eventid) <= 0);
            }

            const eventids = resultData?.map((item: RK_API.Problem) => item.eventid);

            if (eventids.length) {
              const currentPageEventIds = eventids.slice(index, index + pageSize);
              getAlert(currentPageEventIds);
            }

            // 收集所有已解决的事件ID
            const resolvedEventIds = resultData
              ?.filter((item: RK_API.Problem) => Number(item.r_eventid) > 0)
              .map((item: RK_API.Problem) => item.r_eventid);

            // 如果有已解决的事件，调用API获取其信息
            if (resolvedEventIds?.length) {
              const currentPageResolvedIds = resolvedEventIds.slice(index, index + pageSize);
              await getResolvedEvents(currentPageResolvedIds);
            }

            const startIndex = index; // (current - 1) * pageSize
            const endIndex = index + pageSize;

            const paginatedData = resultData?.slice(startIndex, endIndex) || [];

            return {
              ...problem,
              data: paginatedData,
              total: resultData.length,
            };
          }}
        />
      </ProConfigProvider>
      {/* 动作 */}
      <ActionModal
        onOpenChange={setModalVisit}
        open={modalVisit}
        initialValues={{
          list: actionRef.current,
        }}
      />
      {/* 更新问题 */}
      <UpdateDrawer
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        onFinish={async () => tableRef.current?.reload()}
        initialValues={problemRef.current}
      />
      {/* markdown内容弹窗 */}
      <MarkdownModal
        setMarkdownModalVisit={setMarkdownModalVisit}
        open={markdownModalVisit}
        content={content}
      />
    </PageContainer>
  );
});

export default Alarm;
